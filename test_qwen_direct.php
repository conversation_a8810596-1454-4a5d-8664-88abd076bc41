<?php
// 直接测试Qwen API调用
$config = require_once 'config.php';

// 测试您使用的具体图片URL
$imageUrl = 'http://solve.igmdns.com/img/1.jpg';

echo "测试Qwen API直接调用...\n";
echo "图片URL: $imageUrl\n";
echo "API URL: " . $config['qwen']['api_url'] . "\n";
echo "模型: " . $config['qwen']['model'] . "\n\n";

$data = [
    'model' => $config['qwen']['model'],
    'input' => [
        'messages' => [
            [
                'role' => $config['qwen']['role'],
                'content' => $config['qwen']['system_content']
            ],
            [
                'role' => 'user',
                'content' => [
                    [
                        'image' => $imageUrl
                    ],
                    [
                        'text' => $config['qwen']['user_prompt_template']
                    ]
                ]
            ]
        ]
    ],
    'parameters' => [
        'temperature' => $config['qwen']['temperature'],
        'detail' => $config['qwen']['detail']
    ]
];

echo "请求数据:\n";
echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $config['qwen']['api_url']);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $config['qwen']['api_key'],
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, $config['qwen']['timeout']);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
if ($curlError) {
    echo "cURL错误: $curlError\n";
}

echo "\n原始响应:\n";
echo $response . "\n\n";

if ($response) {
    $decoded = json_decode($response, true);
    if ($decoded) {
        echo "解析后的响应:\n";
        echo json_encode($decoded, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        
        if (isset($decoded['output']['choices'][0]['message']['content'])) {
            echo "\n识别结果:\n";
            echo $decoded['output']['choices'][0]['message']['content'] . "\n";
        } elseif (isset($decoded['error'])) {
            echo "\nAPI错误:\n";
            echo json_encode($decoded['error'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        }
    } else {
        echo "JSON解析失败: " . json_last_error_msg() . "\n";
    }
}
?>
