<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 加载配置
$config = require_once 'config.php';

// 确保日志目录存在
$logDir = dirname($config['log_file']);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// 主要处理函数
function processExamImage($imageUrl, $config) {
    $result = [
        'success' => false,
        'qwen_response' => null,
        'deepseek_response' => null,
        'error' => null
    ];
    
    try {
        // 步骤1: 调用qwen-vl-plus识别图片
        $qwenResponse = callQwenAPI($imageUrl, $config['qwen']);
        if (!$qwenResponse) {
            throw new Exception('Qwen API调用失败');
        }
        
        $result['qwen_response'] = $qwenResponse;
        logAPICall('qwen', $qwenResponse, $config['log_file']);
        
        // 提取qwen返回的内容
        $qwenContent = extractQwenContent($qwenResponse);
        
        // 步骤2: 将qwen结果提交给deepseek-chat
        $deepseekResponse = callDeepseekAPI($qwenContent, $config['deepseek']);
        if (!$deepseekResponse) {
            throw new Exception('DeepSeek API调用失败');
        }
        
        $result['deepseek_response'] = $deepseekResponse;
        logAPICall('deepseek', $deepseekResponse, $config['log_file']);
        
        $result['success'] = true;
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
        logAPICall('error', $e->getMessage(), $config['log_file']);
    }
    
    return $result;
}

// 调用Qwen API
function callQwenAPI($imageUrl, $qwenConfig) {
    $data = [
        'model' => $qwenConfig['model'],
        'input' => [
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $qwenConfig['system_content']
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'image' => $imageUrl
                        ],
                        [
                            'text' => '请按照以下格式输出：
1. 题目类型：（单选题/多选题/判断题）
2. 题目内容：（完整的题干）
3. 选项内容：（所有选项的完整内容）
4. 题干图片：（如果题目下方有图片，请描述图片内容）

要求：
- 完整准确地识别所有文字内容
- 不要解答题目，只需要识别题目内容
- 保持原有的格式和选项标识（A、B、C、D或Y、N）'
                        ]
                    ]
                ]
            ]
        ],
        'parameters' => [
            'temperature' => $qwenConfig['temperature']
        ]
    ];
    
    return makeAPICall($qwenConfig['api_url'], $data, [
        'Authorization: Bearer ' . $qwenConfig['api_key'],
        'Content-Type: application/json'
    ]);
}

// 调用DeepSeek API
function callDeepseekAPI($questionContent, $deepseekConfig) {
    $data = [
        'model' => $deepseekConfig['model'],
        'messages' => [
            [
                'role' => 'system',
                'content' => $deepseekConfig['system_content']
            ],
            [
                'role' => 'user',
                'content' => $questionContent . "\n\n请严格按照以下格式回复：\n\n题目类型：[单选题/多选题/判断题]\n题目内容：[问题的完整内容，不包含问题序号]\n选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]\n正确答案：[A,B,C,D] [如果是判断题则为Y或者N]\n答案解析：[答案解析内容]\n\n所有的标点符号都需要用英文符号。不可以出现多余的空格。"
            ]
        ],
        'temperature' => $deepseekConfig['temperature']
    ];
    
    return makeAPICall($deepseekConfig['api_url'], $data, [
        'Authorization: Bearer ' . $deepseekConfig['api_key'],
        'Content-Type: application/json'
    ]);
}

// 通用API调用函数
function makeAPICall($url, $data, $headers) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false || $httpCode !== 200) {
        return false;
    }
    
    return json_decode($response, true);
}

// 提取Qwen返回内容
function extractQwenContent($qwenResponse) {
    if (isset($qwenResponse['output']['choices'][0]['message']['content'])) {
        return $qwenResponse['output']['choices'][0]['message']['content'];
    }
    return json_encode($qwenResponse);
}

// 记录API调用日志
function logAPICall($type, $data, $logFile) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $type: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 处理请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['image_url']) || empty($input['image_url'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少image_url参数']);
        exit();
    }
    
    $result = processExamImage($input['image_url'], $config);
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // 显示使用说明
    echo json_encode([
        'message' => '驾照考试题目识别API',
        'usage' => [
            'method' => 'POST',
            'content_type' => 'application/json',
            'body' => [
                'image_url' => '图片URL地址'
            ]
        ],
        'curl_example' => 'curl -X POST -H "Content-Type: application/json" -d \'{"image_url":"https://example.com/image.jpg"}\' http://your-domain.com/index.php'
    ], JSON_UNESCAPED_UNICODE);
    
} else {
    http_response_code(405);
    echo json_encode(['error' => '不支持的请求方法']);
}
?>
