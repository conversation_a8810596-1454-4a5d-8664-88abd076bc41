<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 加载配置
$config = require_once 'config.php';

// 确保日志目录存在
$logDir = dirname($config['system']['log_file']);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// 主要处理函数
function processExamImage($imageUrl, $config) {
    $result = [
        'success' => false,
        'qwen_response' => null,
        'deepseek_response' => null,
        'error' => null
    ];
    
    try {
        // 步骤1: 调用qwen-vl-plus识别图片
        $qwenResponse = callQwenAPI($imageUrl, $config['qwen']);
        if (!$qwenResponse) {
            throw new Exception('Qwen API调用失败');
        }
        
        $result['qwen_response'] = $qwenResponse;
        logAPICall('qwen', $qwenResponse, $config['system']['log_file']);
        
        // 提取qwen返回的内容
        $qwenContent = extractQwenContent($qwenResponse);
        
        // 步骤2: 将qwen结果提交给deepseek-chat
        $deepseekResponse = callDeepseekAPI($qwenContent, $config['deepseek']);
        if (!$deepseekResponse) {
            throw new Exception('DeepSeek API调用失败');
        }
        
        $result['deepseek_response'] = $deepseekResponse;
        logAPICall('deepseek', $deepseekResponse, $config['system']['log_file']);
        
        $result['success'] = true;
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
        logAPICall('error', $e->getMessage(), $config['system']['log_file']);
    }
    
    return $result;
}

// 调用Qwen API
function callQwenAPI($imageUrl, $qwenConfig) {
    $data = [
        'model' => $qwenConfig['model'],
        'input' => [
            'messages' => [
                [
                    'role' => $qwenConfig['role'],
                    'content' => $qwenConfig['system_content']
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'image' => $imageUrl
                        ],
                        [
                            'text' => $qwenConfig['user_prompt_template']
                        ]
                    ]
                ]
            ]
        ],
        'parameters' => [
            'temperature' => $qwenConfig['temperature'],
            'detail' => $qwenConfig['detail']
        ]
    ];

    return makeAPICall(
        $qwenConfig['api_url'],
        $data,
        [
            'Authorization: Bearer ' . $qwenConfig['api_key'],
            'Content-Type: application/json'
        ],
        $qwenConfig['timeout']
    );
}

// 调用DeepSeek API
function callDeepseekAPI($questionContent, $deepseekConfig) {
    $data = [
        'model' => $deepseekConfig['model'],
        'messages' => [
            [
                'role' => $deepseekConfig['role'],
                'content' => $deepseekConfig['system_content']
            ],
            [
                'role' => 'user',
                'content' => $questionContent . $deepseekConfig['user_prompt_template']
            ]
        ],
        'temperature' => $deepseekConfig['temperature'],
        'max_tokens' => $deepseekConfig['max_tokens'],
        'top_p' => $deepseekConfig['top_p'],
        'frequency_penalty' => $deepseekConfig['frequency_penalty'],
        'presence_penalty' => $deepseekConfig['presence_penalty']
    ];

    return makeAPICall(
        $deepseekConfig['api_url'],
        $data,
        [
            'Authorization: Bearer ' . $deepseekConfig['api_key'],
            'Content-Type: application/json'
        ],
        $deepseekConfig['timeout']
    );
}

// 通用API调用函数
function makeAPICall($url, $data, $headers, $timeout = 60) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if ($response === false) {
        error_log("cURL Error: " . $curlError);
        return false;
    }

    if ($httpCode !== 200) {
        error_log("HTTP Error: " . $httpCode . " Response: " . $response);
        return false;
    }

    return json_decode($response, true);
}

// 提取Qwen返回内容
function extractQwenContent($qwenResponse) {
    if (isset($qwenResponse['output']['choices'][0]['message']['content'])) {
        return $qwenResponse['output']['choices'][0]['message']['content'];
    }
    return json_encode($qwenResponse);
}

// 记录API调用日志
function logAPICall($type, $data, $logFile) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $type: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 处理请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['image_url']) || empty($input['image_url'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少image_url参数']);
        exit();
    }
    
    $result = processExamImage($input['image_url'], $config);
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // 显示使用说明
    echo json_encode([
        'message' => '驾照考试题目识别API',
        'usage' => [
            'method' => 'POST',
            'content_type' => 'application/json',
            'body' => [
                'image_url' => '图片URL地址'
            ]
        ],
        'curl_example' => 'curl -X POST -H "Content-Type: application/json" -d \'{"image_url":"https://example.com/image.jpg"}\' http://your-domain.com/index.php'
    ], JSON_UNESCAPED_UNICODE);
    
} else {
    http_response_code(405);
    echo json_encode(['error' => '不支持的请求方法']);
}
?>
