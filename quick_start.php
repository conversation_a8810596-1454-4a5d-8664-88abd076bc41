<?php
// 快速启动脚本
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>驾照考试题目识别API - 快速启动</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .btn:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status.ok { background: #d4edda; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚗 驾照考试题目识别API</h1>
        <h2>快速启动向导</h2>";

// 检查系统状态
$checks = [];

// 检查配置文件
if (file_exists('config.php')) {
    $config = require 'config.php';
    $checks['config'] = true;
    
    // 检查API密钥
    $qwenKeySet = !empty($config['qwen']['api_key']) && $config['qwen']['api_key'] !== 'YOUR_QWEN_API_KEY_HERE';
    $deepseekKeySet = !empty($config['deepseek']['api_key']) && $config['deepseek']['api_key'] !== 'YOUR_DEEPSEEK_API_KEY_HERE';
    
    $checks['qwen_key'] = $qwenKeySet;
    $checks['deepseek_key'] = $deepseekKeySet;
    $checks['test_mode'] = $config['test']['enable_test_mode'] ?? false;
} else {
    $checks['config'] = false;
}

// 检查日志目录
$checks['logs'] = is_dir('logs') && is_writable('logs');

// 显示状态
echo "<div class='step'>";
echo "<h3>📋 系统状态检查</h3>";

if ($checks['config']) {
    echo "<div class='status ok'>✅ 配置文件存在</div>";
} else {
    echo "<div class='status error'>❌ 配置文件不存在</div>";
}

if (isset($checks['qwen_key'])) {
    if ($checks['qwen_key']) {
        echo "<div class='status ok'>✅ Qwen API密钥已配置</div>";
    } else {
        echo "<div class='status warning'>⚠️ Qwen API密钥未配置</div>";
    }
}

if (isset($checks['deepseek_key'])) {
    if ($checks['deepseek_key']) {
        echo "<div class='status ok'>✅ DeepSeek API密钥已配置</div>";
    } else {
        echo "<div class='status warning'>⚠️ DeepSeek API密钥未配置</div>";
    }
}

if ($checks['logs']) {
    echo "<div class='status ok'>✅ 日志目录可写</div>";
} else {
    echo "<div class='status error'>❌ 日志目录不可写</div>";
}

if (isset($checks['test_mode']) && $checks['test_mode']) {
    echo "<div class='status warning'>🧪 测试模式已启用（使用模拟数据）</div>";
} else {
    echo "<div class='status ok'>🔴 生产模式（使用真实API）</div>";
}

echo "</div>";

// 显示步骤
echo "<div class='step'>
    <h3>🚀 快速开始步骤</h3>
    <ol>
        <li><strong>系统检查</strong> - 运行完整的系统检查</li>
        <li><strong>配置API</strong> - 设置API密钥和参数</li>
        <li><strong>测试功能</strong> - 验证系统工作正常</li>
        <li><strong>查看日志</strong> - 监控API调用情况</li>
    </ol>
</div>";

// 显示操作按钮
echo "<div class='step'>
    <h3>🎯 快速操作</h3>
    <a href='check_setup.php' class='btn'>🔍 系统检查</a>
    <a href='config_manager.php' class='btn'>⚙️ 配置管理</a>
    <a href='test.html' class='btn'>🧪 测试API</a>
    <a href='logs/view_logs.php' class='btn'>📋 查看日志</a>
</div>";

// 显示建议
echo "<div class='step'>
    <h3>💡 建议操作流程</h3>";

if (!$checks['config']) {
    echo "<p class='error'>❌ 请先创建配置文件</p>";
} elseif (!isset($checks['qwen_key']) || !$checks['qwen_key'] || !$checks['deepseek_key']) {
    echo "<p class='warning'>⚠️ 建议先配置API密钥，或启用测试模式进行功能验证</p>";
    echo "<p><strong>选项1：</strong> <a href='config_manager.php'>配置真实API密钥</a></p>";
    echo "<p><strong>选项2：</strong> 启用测试模式（在配置管理器中设置）</p>";
} elseif (isset($checks['test_mode']) && $checks['test_mode']) {
    echo "<p class='success'>✅ 测试模式已启用，可以直接测试功能</p>";
    echo "<p>1. <a href='test.html'>测试API功能</a></p>";
    echo "<p>2. 验证无误后，在配置管理器中关闭测试模式</p>";
} else {
    echo "<p class='success'>✅ 系统配置完成，可以开始使用</p>";
    echo "<p>1. <a href='test.html'>测试API功能</a></p>";
    echo "<p>2. <a href='logs/view_logs.php'>查看调用日志</a></p>";
}

echo "</div>";

// 显示cURL示例
echo "<div class='step'>
    <h3>📡 cURL使用示例</h3>
    <pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>
curl -X POST \\
  -H \"Content-Type: application/json\" \\
  -d '{\"image_url\":\"https://example.com/exam-image.jpg\"}' \\
  http://your-domain.com/index.php
    </pre>
</div>";

// 显示帮助链接
echo "<div class='step'>
    <h3>📚 帮助文档</h3>
    <p><a href='README.md'>📖 完整使用说明</a></p>
    <p><a href='TROUBLESHOOTING.md'>🔧 故障排除指南</a></p>
    <p><strong>技术约束配置：</strong> 所有model、role、content、temperature参数都可在配置管理器中调整</p>
</div>";

echo "    </div>
</body>
</html>";
?>
