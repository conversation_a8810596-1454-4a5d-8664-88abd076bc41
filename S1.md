不依赖php任何框架给我实现以下功能；

1. 将图片Url提交给qwen-vl-plus；


2. 将qwen返回的原始数据提交给deepseek-chat。


3. 将qwen跟deepseek返回的原始数据输出到一个log文件。


4. 让这个网站支持curl，并告诉我curl的格式。

5. 需要对qwen的技术约束；
    model = qwen-vl-plus
    role = system
    content = 你是一个专业的图文专家
    temperature = 0
    detail = high


6. 需要对deepseek的技术约束；
    model = deepseek-chat
    role = system
    content = 你是一个专业的驾照科目考试专家
    temperature = 0

7. 使用 HTTP REST API 调用qwen跟deepseek。

8. 
图片的解释；

所有的图片都是一个考试系统的截图或者照片，图片内容有考试题。需要qwen精准的识别图中的考题与题干图片，

问题示例样本，共有三种问题。三种题型都有可能存在图片，题干图片位置在问题选项的下方。

(单选题)
驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？
A、减速鸣喇叭示意
B、迅速行驶到坡顶以改善视距
C、长时间开启远光灯提醒对向来车
D、不间断鸣喇叭并加速冲坡


(多选题)
当你驾车准备进入拥堵的环形路口时，以下哪些做法是不安全的?
A、继续驶入拥堵路口
B、让路口内的车先行
C、快速驶入路口
D、鸣喇叭催促路口内车辆


(判断题)
驾驶人在道路上行驶，要时刻留意人行横道标志，遇有行人通过人行横道时，应停车让行。
Y：正确
N：错误


对于qwen-vl-plus的返回结果，我期望的是完整且精准的识别出考试问题与选项的问题。不需要qwen给我解答问题本身。

9. 
对于deepseek-chat的返回结果，我期望可以得到置信度为百分百的答案，同时能严格按照如下格式来进行回复。


        题目类型：[单选题/多选题/判断题]
        题目内容：[问题的完整内容，不包含问题序号]
        选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]
        正确答案；[A,B,C,D] [如果是判断题则为Y或者N]
        答案解析：[答案解析内容]

        所有的标点符号都需要用英文符号。不可以出现多余的空格。
