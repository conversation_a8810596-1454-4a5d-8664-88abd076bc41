<?php
// 系统部署检查脚本
header('Content-Type: text/html; charset=utf-8');

$checks = [];
$allPassed = true;

// 检查PHP版本
$phpVersion = phpversion();
$checks['php_version'] = [
    'name' => 'PHP版本',
    'status' => version_compare($phpVersion, '7.4.0', '>='),
    'message' => "当前版本: $phpVersion " . (version_compare($phpVersion, '7.4.0', '>=') ? '✅' : '❌ 需要7.4+'),
    'required' => true
];

// 检查cURL扩展
$checks['curl'] = [
    'name' => 'cURL扩展',
    'status' => extension_loaded('curl'),
    'message' => extension_loaded('curl') ? '✅ 已安装' : '❌ 未安装',
    'required' => true
];

// 检查JSON扩展
$checks['json'] = [
    'name' => 'JSON扩展',
    'status' => extension_loaded('json'),
    'message' => extension_loaded('json') ? '✅ 已安装' : '❌ 未安装',
    'required' => true
];

// 检查配置文件
$checks['config'] = [
    'name' => '配置文件',
    'status' => file_exists('config.php'),
    'message' => file_exists('config.php') ? '✅ 存在' : '❌ 不存在',
    'required' => true
];

// 检查API密钥配置
if (file_exists('config.php')) {
    $config = require 'config.php';
    $qwenKeySet = !empty($config['qwen']['api_key']) && $config['qwen']['api_key'] !== 'YOUR_QWEN_API_KEY_HERE';
    $deepseekKeySet = !empty($config['deepseek']['api_key']) && $config['deepseek']['api_key'] !== 'YOUR_DEEPSEEK_API_KEY_HERE';
    
    $checks['qwen_key'] = [
        'name' => 'Qwen API密钥',
        'status' => $qwenKeySet,
        'message' => $qwenKeySet ? '✅ 已配置' : '❌ 未配置',
        'required' => true
    ];
    
    $checks['deepseek_key'] = [
        'name' => 'DeepSeek API密钥',
        'status' => $deepseekKeySet,
        'message' => $deepseekKeySet ? '✅ 已配置' : '❌ 未配置',
        'required' => true
    ];
}

// 检查日志目录
$checks['log_dir'] = [
    'name' => '日志目录',
    'status' => is_dir('logs'),
    'message' => is_dir('logs') ? '✅ 存在' : '❌ 不存在',
    'required' => true
];

// 检查日志目录写权限
if (is_dir('logs')) {
    $checks['log_writable'] = [
        'name' => '日志目录写权限',
        'status' => is_writable('logs'),
        'message' => is_writable('logs') ? '✅ 可写' : '❌ 不可写',
        'required' => true
    ];
}

// 检查主文件
$requiredFiles = ['index.php', 'config.php', 'test.php', 'test.html'];
foreach ($requiredFiles as $file) {
    $checks["file_$file"] = [
        'name' => "文件: $file",
        'status' => file_exists($file),
        'message' => file_exists($file) ? '✅ 存在' : '❌ 不存在',
        'required' => true
    ];
}

// 统计结果
foreach ($checks as $check) {
    if ($check['required'] && !$check['status']) {
        $allPassed = false;
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统部署检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-overall {
            text-align: center;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: bold;
        }
        .status-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .check-item.pass {
            background-color: #f8fff8;
            border-color: #c3e6cb;
        }
        .check-item.fail {
            background-color: #fff8f8;
            border-color: #f5c6cb;
        }
        .check-name {
            font-weight: bold;
            color: #333;
        }
        .check-message {
            color: #666;
        }
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.success:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 系统部署检查</h1>
        
        <div class="status-overall <?php echo $allPassed ? 'status-pass' : 'status-fail'; ?>">
            <?php if ($allPassed): ?>
                🎉 所有检查项目都通过了！系统已准备就绪。
            <?php else: ?>
                ⚠️ 发现问题，请修复后重新检查。
            <?php endif; ?>
        </div>
        
        <?php foreach ($checks as $key => $check): ?>
            <div class="check-item <?php echo $check['status'] ? 'pass' : 'fail'; ?>">
                <div class="check-name"><?php echo htmlspecialchars($check['name']); ?></div>
                <div class="check-message"><?php echo $check['message']; ?></div>
            </div>
        <?php endforeach; ?>
        
        <div class="actions">
            <a href="check_setup.php" class="btn">🔄 重新检查</a>
            <?php if ($allPassed): ?>
                <a href="test.html" class="btn success">🚀 开始测试</a>
                <a href="logs/view_logs.php" class="btn">📋 查看日志</a>
            <?php endif; ?>
        </div>
        
        <?php if (!$allPassed): ?>
            <div style="margin-top: 30px; padding: 20px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                <h3>🛠️ 修复建议：</h3>
                <ul>
                    <?php if (!$checks['config']['status']): ?>
                        <li>创建 config.php 文件</li>
                    <?php endif; ?>
                    
                    <?php if (isset($checks['qwen_key']) && !$checks['qwen_key']['status']): ?>
                        <li>在 config.php 中配置 Qwen API 密钥</li>
                    <?php endif; ?>
                    
                    <?php if (isset($checks['deepseek_key']) && !$checks['deepseek_key']['status']): ?>
                        <li>在 config.php 中配置 DeepSeek API 密钥</li>
                    <?php endif; ?>
                    
                    <?php if (!$checks['log_dir']['status']): ?>
                        <li>创建 logs 目录: <code>mkdir logs</code></li>
                    <?php endif; ?>
                    
                    <?php if (isset($checks['log_writable']) && !$checks['log_writable']['status']): ?>
                        <li>设置日志目录权限: <code>chmod 755 logs</code></li>
                    <?php endif; ?>
                    
                    <?php if (!$checks['curl']['status']): ?>
                        <li>安装 PHP cURL 扩展</li>
                    <?php endif; ?>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
