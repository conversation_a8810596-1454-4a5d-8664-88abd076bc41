# 驾照考试题目识别API系统

这是一个基于PHP的驾照考试题目识别和解答系统，不依赖任何PHP框架，使用Qwen-VL-Plus进行图像识别，DeepSeek-Chat进行题目解答。

## 功能特性

- 🖼️ **图像识别**: 使用Qwen-VL-Plus识别驾照考试题目图片
- 🧠 **智能解答**: 使用DeepSeek-Chat提供专业的题目解答
- 📝 **日志记录**: 完整记录所有API调用的原始数据
- 🌐 **REST API**: 支持HTTP REST API调用
- 📱 **cURL支持**: 完全支持cURL命令行调用

## 系统要求

- PHP 7.4 或更高版本
- cURL 扩展
- 可写的日志目录权限

## 安装配置

### 1. 下载文件
将所有文件放置到您的Web服务器目录中。

### 2. 配置API密钥
编辑 `config.php` 文件，填入您的API密钥：

```php
'qwen' => [
    'api_key' => 'YOUR_QWEN_API_KEY_HERE', // 替换为您的Qwen API密钥
],
'deepseek' => [
    'api_key' => 'YOUR_DEEPSEEK_API_KEY_HERE', // 替换为您的DeepSeek API密钥
],
```

### 3. 设置权限
确保日志目录可写：
```bash
chmod 755 logs/
chmod 666 logs/api_logs.txt
```

## API使用方法

### 基本用法

**端点**: `POST /index.php`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
    "image_url": "https://example.com/exam-image.jpg"
}
```

### cURL命令示例

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"image_url":"https://example.com/exam-image.jpg"}' \
  http://your-domain.com/index.php
```

### 响应格式

**成功响应**:
```json
{
    "success": true,
    "qwen_response": {
        // Qwen-VL-Plus的完整响应数据
    },
    "deepseek_response": {
        // DeepSeek-Chat的完整响应数据
    }
}
```

**错误响应**:
```json
{
    "success": false,
    "error": "错误信息",
    "qwen_response": null,
    "deepseek_response": null
}
```

## 题目识别格式

### Qwen-VL-Plus输出格式
```
1. 题目类型：（单选题/多选题/判断题）
2. 题目内容：（完整的题干）
3. 选项内容：（所有选项的完整内容）
4. 题干图片：（如果题目下方有图片，请描述图片内容）
```

### DeepSeek-Chat输出格式
```
题目类型：[单选题/多选题/判断题]
题目内容：[问题的完整内容，不包含问题序号]
选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]
正确答案：[A,B,C,D] [如果是判断题则为Y或者N]
答案解析：[答案解析内容]
```

## 支持的题目类型

### 1. 单选题
```
驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？
A、减速鸣喇叭示意
B、迅速行驶到坡顶以改善视距
C、长时间开启远光灯提醒对向来车
D、不间断鸣喇叭并加速冲坡
```

### 2. 多选题
```
当你驾车准备进入拥堵的环形路口时，以下哪些做法是不安全的?
A、继续驶入拥堵路口
B、让路口内的车先行
C、快速驶入路口
D、鸣喇叭催促路口内车辆
```

### 3. 判断题
```
驾驶人在道路上行驶，要时刻留意人行横道标志，遇有行人通过人行横道时，应停车让行。
Y：正确
N：错误
```

## 测试

运行测试脚本：
```bash
php test.php
```

或通过浏览器访问：
```
http://your-domain.com/test.php
```

## 日志文件

所有API调用都会记录在 `logs/api_logs.txt` 文件中，包括：
- 时间戳
- API类型（qwen/deepseek/error）
- 完整的请求和响应数据

## 技术规格

### Qwen-VL-Plus配置
- **模型**: qwen-vl-plus
- **角色**: system
- **温度**: 0
- **细节**: high
- **专业领域**: 图文识别专家

### DeepSeek-Chat配置
- **模型**: deepseek-chat
- **角色**: system
- **温度**: 0
- **专业领域**: 驾照科目考试专家

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `config.php` 中的API密钥是否正确
   - 确认API密钥有足够的权限和余额

2. **日志文件无法写入**
   - 检查 `logs/` 目录权限
   - 确保Web服务器有写入权限

3. **cURL错误**
   - 检查服务器是否支持cURL扩展
   - 确认网络连接正常

4. **超时错误**
   - 增加PHP的 `max_execution_time`
   - 检查API服务是否正常

### 调试模式

在 `config.php` 中添加调试选项：
```php
'debug' => true,
'verbose_logging' => true
```

## 许可证

本项目仅供学习和研究使用。

## 联系支持

如有问题，请检查日志文件或联系技术支持。
