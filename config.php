<?php
// API配置文件
// 请在此处填入您的API密钥和调整技术参数

return [
    // ===== Qwen-VL-Plus 配置 =====
    'qwen' => [
        // API基础配置
        'api_url' => 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
        'api_key' => 'sk-3920274bedf642c2b7495f534aadca84', // 您的Qwen API密钥

        // 模型技术约束参数（按S1.md要求）
        'model' => 'qwen-vl-plus',
        'role' => 'system',
        'temperature' => 0,
        'detail' => 'high', // 图像识别细节级别

        // 系统提示词配置
        'system_content' => '你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项等信息。不需要解答题目，只需要完整准确地识别所有文字内容。',

        // 用户提示词模板
        'user_prompt_template' => '请按照以下格式输出：
1. 题目类型：（单选题/多选题/判断题）
2. 题目内容：（完整的题干）
3. 选项内容：（所有选项的完整内容）
4. 题干图片：（如果题目下方有图片，请描述图片内容）

要求：
- 完整准确地识别所有文字内容
- 不要解答题目，只需要识别题目内容
- 保持原有的格式和选项标识（A、B、C、D或Y、N）',

        // API调用参数
        'timeout' => 60,
        'max_retries' => 3,
    ],

    // ===== DeepSeek-Chat 配置 =====
    'deepseek' => [
        // API基础配置
        'api_url' => 'https://api.deepseek.com/chat/completions',
        'api_key' => 'sk-dd3347aa018244b1a2e19bb364c3c97e', // 您的DeepSeek API密钥

        // 模型技术约束参数（按S1.md要求）
        'model' => 'deepseek-chat',
        'role' => 'system',
        'temperature' => 0,
        'max_tokens' => 2000, // 最大输出token数
        'top_p' => 1.0,
        'frequency_penalty' => 0,
        'presence_penalty' => 0,

        // 系统提示词配置
        'system_content' => '你是一个专业的驾照科目考试专家，具有丰富的交通法规知识和考试经验。请根据提供的题目内容，给出准确的答案和详细的解析。',

        // 用户提示词模板
        'user_prompt_template' => '

请严格按照以下格式回复：

题目类型：[单选题/多选题/判断题]
题目内容：[问题的完整内容，不包含问题序号]
选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]
正确答案：[A,B,C,D] [如果是判断题则为Y或者N]
答案解析：[答案解析内容]

所有的标点符号都需要用英文符号。不可以出现多余的空格。',

        // API调用参数
        'timeout' => 60,
        'max_retries' => 3,
    ],

    // ===== 系统配置 =====
    'system' => [
        'log_file' => 'logs/api_logs.txt',
        'max_log_size' => 10 * 1024 * 1024, // 10MB
        'debug_mode' => false, // 调试模式
        'verbose_logging' => true, // 详细日志
        'api_timeout' => 120, // 总体API超时时间（秒）
    ],

    // ===== 测试配置 =====
    'test' => [
        'sample_image_urls' => [
            'https://example.com/single-choice.jpg',
            'https://example.com/multiple-choice.jpg',
            'https://example.com/true-false.jpg'
        ],
        'enable_test_mode' => false, // 测试模式（使用模拟数据）
    ]
];
?>
