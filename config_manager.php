<?php
// 配置管理器 - 方便测试和修改不同的配置参数
header('Content-Type: text/html; charset=utf-8');

// 加载当前配置
$config = require_once 'config.php';

// 处理配置更新
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_config') {
        $newConfig = $config;
        
        // 更新Qwen配置
        if (isset($_POST['qwen'])) {
            foreach ($_POST['qwen'] as $key => $value) {
                if ($key === 'temperature') {
                    $newConfig['qwen'][$key] = (float)$value;
                } elseif ($key === 'timeout' || $key === 'max_retries') {
                    $newConfig['qwen'][$key] = (int)$value;
                } else {
                    $newConfig['qwen'][$key] = $value;
                }
            }
        }
        
        // 更新DeepSeek配置
        if (isset($_POST['deepseek'])) {
            foreach ($_POST['deepseek'] as $key => $value) {
                if (in_array($key, ['temperature', 'top_p', 'frequency_penalty', 'presence_penalty'])) {
                    $newConfig['deepseek'][$key] = (float)$value;
                } elseif (in_array($key, ['max_tokens', 'timeout', 'max_retries'])) {
                    $newConfig['deepseek'][$key] = (int)$value;
                } else {
                    $newConfig['deepseek'][$key] = $value;
                }
            }
        }
        
        // 更新系统配置
        if (isset($_POST['system'])) {
            foreach ($_POST['system'] as $key => $value) {
                if ($key === 'debug_mode' || $key === 'verbose_logging') {
                    $newConfig['system'][$key] = $value === 'true';
                } elseif (in_array($key, ['max_log_size', 'api_timeout'])) {
                    $newConfig['system'][$key] = (int)$value;
                } else {
                    $newConfig['system'][$key] = $value;
                }
            }
        }
        
        // 保存配置
        $configContent = "<?php\n// API配置文件\n// 请在此处填入您的API密钥和调整技术参数\n\nreturn " . var_export($newConfig, true) . ";\n";
        file_put_contents('config.php', $configContent);
        
        $message = "✅ 配置已更新！";
        $config = $newConfig; // 重新加载配置
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="number"], input[type="url"], select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .current-value {
            font-size: 12px;
            color: #666;
            font-style: italic;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .preset-buttons {
            margin-bottom: 20px;
        }
        .preset-btn {
            background-color: #6c757d;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .preset-btn:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚙️ 配置管理器</h1>
        
        <?php if (isset($message)): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <div class="preset-buttons">
            <h3>🎯 预设配置</h3>
            <button type="button" class="preset-btn" onclick="loadPreset('strict')">严格模式 (温度=0)</button>
            <button type="button" class="preset-btn" onclick="loadPreset('creative')">创意模式 (温度=0.3)</button>
            <button type="button" class="preset-btn" onclick="loadPreset('balanced')">平衡模式 (温度=0.1)</button>
            <button type="button" class="preset-btn" onclick="loadPreset('debug')">调试模式</button>
        </div>
        
        <form method="POST">
            <input type="hidden" name="action" value="update_config">
            
            <!-- Qwen-VL-Plus 配置 -->
            <div class="config-section">
                <h2>🖼️ Qwen-VL-Plus 配置</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="qwen_model">模型名称:</label>
                        <input type="text" id="qwen_model" name="qwen[model]" value="<?php echo htmlspecialchars($config['qwen']['model']); ?>">
                        <div class="current-value">当前: <?php echo $config['qwen']['model']; ?></div>
                    </div>
                    <div class="form-group">
                        <label for="qwen_role">角色:</label>
                        <select id="qwen_role" name="qwen[role]">
                            <option value="system" <?php echo $config['qwen']['role'] === 'system' ? 'selected' : ''; ?>>system</option>
                            <option value="user" <?php echo $config['qwen']['role'] === 'user' ? 'selected' : ''; ?>>user</option>
                            <option value="assistant" <?php echo $config['qwen']['role'] === 'assistant' ? 'selected' : ''; ?>>assistant</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="qwen_temperature">温度 (0-1):</label>
                        <input type="number" id="qwen_temperature" name="qwen[temperature]" value="<?php echo $config['qwen']['temperature']; ?>" min="0" max="1" step="0.1">
                        <div class="current-value">当前: <?php echo $config['qwen']['temperature']; ?></div>
                    </div>
                    <div class="form-group">
                        <label for="qwen_detail">细节级别:</label>
                        <select id="qwen_detail" name="qwen[detail]">
                            <option value="low" <?php echo $config['qwen']['detail'] === 'low' ? 'selected' : ''; ?>>low</option>
                            <option value="high" <?php echo $config['qwen']['detail'] === 'high' ? 'selected' : ''; ?>>high</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="qwen_system_content">系统提示词:</label>
                    <textarea id="qwen_system_content" name="qwen[system_content]"><?php echo htmlspecialchars($config['qwen']['system_content']); ?></textarea>
                </div>
                
                <div class="form-group">
                    <label for="qwen_user_prompt">用户提示词模板:</label>
                    <textarea id="qwen_user_prompt" name="qwen[user_prompt_template]"><?php echo htmlspecialchars($config['qwen']['user_prompt_template']); ?></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="qwen_timeout">超时时间(秒):</label>
                        <input type="number" id="qwen_timeout" name="qwen[timeout]" value="<?php echo $config['qwen']['timeout']; ?>" min="10" max="300">
                    </div>
                    <div class="form-group">
                        <label for="qwen_max_retries">最大重试次数:</label>
                        <input type="number" id="qwen_max_retries" name="qwen[max_retries]" value="<?php echo $config['qwen']['max_retries']; ?>" min="0" max="10">
                    </div>
                </div>
            </div>
            
            <!-- DeepSeek-Chat 配置 -->
            <div class="config-section">
                <h2>🧠 DeepSeek-Chat 配置</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="deepseek_model">模型名称:</label>
                        <input type="text" id="deepseek_model" name="deepseek[model]" value="<?php echo htmlspecialchars($config['deepseek']['model']); ?>">
                        <div class="current-value">当前: <?php echo $config['deepseek']['model']; ?></div>
                    </div>
                    <div class="form-group">
                        <label for="deepseek_role">角色:</label>
                        <select id="deepseek_role" name="deepseek[role]">
                            <option value="system" <?php echo $config['deepseek']['role'] === 'system' ? 'selected' : ''; ?>>system</option>
                            <option value="user" <?php echo $config['deepseek']['role'] === 'user' ? 'selected' : ''; ?>>user</option>
                            <option value="assistant" <?php echo $config['deepseek']['role'] === 'assistant' ? 'selected' : ''; ?>>assistant</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="deepseek_temperature">温度 (0-1):</label>
                        <input type="number" id="deepseek_temperature" name="deepseek[temperature]" value="<?php echo $config['deepseek']['temperature']; ?>" min="0" max="1" step="0.1">
                        <div class="current-value">当前: <?php echo $config['deepseek']['temperature']; ?></div>
                    </div>
                    <div class="form-group">
                        <label for="deepseek_max_tokens">最大Token数:</label>
                        <input type="number" id="deepseek_max_tokens" name="deepseek[max_tokens]" value="<?php echo $config['deepseek']['max_tokens']; ?>" min="100" max="8000">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="deepseek_top_p">Top P (0-1):</label>
                        <input type="number" id="deepseek_top_p" name="deepseek[top_p]" value="<?php echo $config['deepseek']['top_p']; ?>" min="0" max="1" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="deepseek_frequency_penalty">频率惩罚 (-2 to 2):</label>
                        <input type="number" id="deepseek_frequency_penalty" name="deepseek[frequency_penalty]" value="<?php echo $config['deepseek']['frequency_penalty']; ?>" min="-2" max="2" step="0.1">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="deepseek_system_content">系统提示词:</label>
                    <textarea id="deepseek_system_content" name="deepseek[system_content]"><?php echo htmlspecialchars($config['deepseek']['system_content']); ?></textarea>
                </div>
                
                <div class="form-group">
                    <label for="deepseek_user_prompt">用户提示词模板:</label>
                    <textarea id="deepseek_user_prompt" name="deepseek[user_prompt_template]"><?php echo htmlspecialchars($config['deepseek']['user_prompt_template']); ?></textarea>
                </div>
            </div>
            
            <!-- 系统配置 -->
            <div class="config-section">
                <h2>🔧 系统配置</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="system_debug_mode">调试模式:</label>
                        <select id="system_debug_mode" name="system[debug_mode]">
                            <option value="false" <?php echo !$config['system']['debug_mode'] ? 'selected' : ''; ?>>关闭</option>
                            <option value="true" <?php echo $config['system']['debug_mode'] ? 'selected' : ''; ?>>开启</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="system_verbose_logging">详细日志:</label>
                        <select id="system_verbose_logging" name="system[verbose_logging]">
                            <option value="false" <?php echo !$config['system']['verbose_logging'] ? 'selected' : ''; ?>>关闭</option>
                            <option value="true" <?php echo $config['system']['verbose_logging'] ? 'selected' : ''; ?>>开启</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="system_api_timeout">API总超时(秒):</label>
                        <input type="number" id="system_api_timeout" name="system[api_timeout]" value="<?php echo $config['system']['api_timeout']; ?>" min="30" max="600">
                    </div>
                    <div class="form-group">
                        <label for="system_max_log_size">最大日志大小(字节):</label>
                        <input type="number" id="system_max_log_size" name="system[max_log_size]" value="<?php echo $config['system']['max_log_size']; ?>" min="1048576">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="test_enable_test_mode">🧪 测试模式:</label>
                        <select id="test_enable_test_mode" name="test[enable_test_mode]">
                            <option value="false" <?php echo !$config['test']['enable_test_mode'] ? 'selected' : ''; ?>>关闭 (使用真实API)</option>
                            <option value="true" <?php echo $config['test']['enable_test_mode'] ? 'selected' : ''; ?>>开启 (使用模拟数据)</option>
                        </select>
                        <div class="current-value">
                            当前: <?php echo $config['test']['enable_test_mode'] ? '🧪 测试模式 (模拟数据)' : '🔴 生产模式 (真实API)'; ?>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>测试模式说明:</label>
                        <div style="font-size: 12px; color: #666; padding: 8px; background: #f8f9fa; border-radius: 3px;">
                            • 开启：使用模拟数据，不调用真实API，不消耗配额<br>
                            • 关闭：调用真实API，需要有效的API密钥和余额
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="actions">
                <button type="submit">💾 保存配置</button>
                <button type="button" onclick="location.href='test.html'">🧪 测试API</button>
                <button type="button" onclick="location.href='logs/view_logs.php'">📋 查看日志</button>
                <button type="button" onclick="location.href='check_setup.php'">🔍 系统检查</button>
            </div>
        </form>
    </div>
    
    <script>
        function loadPreset(preset) {
            switch(preset) {
                case 'strict':
                    document.getElementById('qwen_temperature').value = 0;
                    document.getElementById('deepseek_temperature').value = 0;
                    document.getElementById('deepseek_top_p').value = 1.0;
                    document.getElementById('system_debug_mode').value = 'false';
                    break;
                case 'creative':
                    document.getElementById('qwen_temperature').value = 0.3;
                    document.getElementById('deepseek_temperature').value = 0.3;
                    document.getElementById('deepseek_top_p').value = 0.9;
                    break;
                case 'balanced':
                    document.getElementById('qwen_temperature').value = 0.1;
                    document.getElementById('deepseek_temperature').value = 0.1;
                    document.getElementById('deepseek_top_p').value = 0.95;
                    break;
                case 'debug':
                    document.getElementById('system_debug_mode').value = 'true';
                    document.getElementById('system_verbose_logging').value = 'true';
                    document.getElementById('qwen_timeout').value = 120;
                    document.getElementById('deepseek_timeout').value = 120;
                    break;
            }
        }
    </script>
</body>
</html>
