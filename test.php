<?php
// 测试脚本
require_once 'config.php';

// 测试图片URL（示例）
$testImageUrl = 'https://example.com/test-exam-image.jpg';

// 测试数据
$testData = [
    'image_url' => $testImageUrl
];

// 发送POST请求到本地API
function testAPI($data) {
    $url = 'http://localhost/index.php'; // 根据您的服务器配置调整
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP状态码: $httpCode\n";
    echo "响应内容:\n";
    echo $response . "\n";
    
    return json_decode($response, true);
}

echo "开始测试驾照考试题目识别API...\n\n";

// 测试GET请求（获取使用说明）
echo "=== 测试GET请求 ===\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/index.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);
echo $response . "\n\n";

// 测试POST请求
echo "=== 测试POST请求 ===\n";
$result = testAPI($testData);

if ($result && isset($result['success'])) {
    if ($result['success']) {
        echo "✅ API调用成功！\n";
        echo "Qwen响应: " . (isset($result['qwen_response']) ? "已收到" : "未收到") . "\n";
        echo "DeepSeek响应: " . (isset($result['deepseek_response']) ? "已收到" : "未收到") . "\n";
    } else {
        echo "❌ API调用失败: " . ($result['error'] ?? '未知错误') . "\n";
    }
} else {
    echo "❌ 无法解析API响应\n";
}

echo "\n测试完成。\n";
?>
