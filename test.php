<?php
// 测试脚本
$config = require_once 'config.php';

// 测试图片URL（使用可访问的驾照考试题目图片）
$testImageUrl = 'http://solve.igmdns.com/img/23.jpg';

// 注意：当前使用真实API，将调用Qwen和DeepSeek进行识别和解答
// 如需测试模式，请在config.php中设置 enable_test_mode => true

// 测试数据
$testData = [
    'image_url' => $testImageUrl
];

echo "使用测试图片: $testImageUrl\n\n";

// 发送POST请求到本地API
function testAPI($data) {
    // 自动检测当前域名
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $url = "$protocol://$host/index.php";

    echo "API端点: $url\n";
    echo "请求数据: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    echo "HTTP状态码: $httpCode\n";
    if ($curlError) {
        echo "cURL错误: $curlError\n";
    }
    echo "响应内容:\n";
    echo $response . "\n\n";

    // 检查响应是否为有效JSON
    $decoded = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "JSON解析错误: " . json_last_error_msg() . "\n";
        echo "原始响应: " . substr($response, 0, 500) . "\n";
        return false;
    }

    return $decoded;
}

echo "开始测试驾照考试题目识别API...\n\n";

// 测试GET请求（获取使用说明）
echo "=== 测试GET请求 ===\n";
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
$getUrl = "$protocol://$host/index.php";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $getUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
$response = curl_exec($ch);
curl_close($ch);
echo $response . "\n\n";

// 测试POST请求
echo "=== 测试POST请求 ===\n";
$result = testAPI($testData);

if ($result && isset($result['success'])) {
    if ($result['success']) {
        echo "✅ API调用成功！\n\n";

        // 显示Qwen统计信息
        if (isset($result['qwen_response'])) {
            $qwen = $result['qwen_response'];
            echo "=== 🖼️ Qwen-VL-Plus 统计 ===\n";
            echo "耗时: " . ($qwen['duration_ms'] ?? '未知') . "ms\n";
            if (isset($qwen['tokens'])) {
                $tokens = $qwen['tokens'];
                echo "Token消费: 总计{$tokens['total_tokens']} (输入{$tokens['input_tokens']} + 输出{$tokens['output_tokens']})\n";
            }
            echo "识别结果: " . (strlen($qwen['content']) > 100 ? "已收到(" . strlen($qwen['content']) . "字符)" : "已收到") . "\n\n";
        }

        // 显示DeepSeek统计信息
        if (isset($result['deepseek_response'])) {
            $deepseek = $result['deepseek_response'];
            echo "=== 🧠 DeepSeek-Chat 统计 ===\n";
            echo "耗时: " . ($deepseek['duration_ms'] ?? '未知') . "ms\n";
            if (isset($deepseek['tokens'])) {
                $tokens = $deepseek['tokens'];
                echo "Token消费: 总计{$tokens['total_tokens']} (输入{$tokens['input_tokens']} + 输出{$tokens['output_tokens']})\n";
            }
            echo "解答结果: " . (strlen($deepseek['content']) > 100 ? "已收到(" . strlen($deepseek['content']) . "字符)" : "已收到") . "\n";
        }

    } else {
        echo "❌ API调用失败: " . ($result['error'] ?? '未知错误') . "\n";
    }
} else {
    echo "❌ 无法解析API响应\n";
}

echo "\n测试完成。\n";
?>
