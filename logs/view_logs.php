<?php
// 日志查看器
header('Content-Type: text/html; charset=utf-8');

$logFile = 'api_logs.txt';
$maxLines = isset($_GET['lines']) ? (int)$_GET['lines'] : 100;

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API日志查看器</title>
    <style>
        body {
            font-family: monospace;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .log-content {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 5px;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 1.4;
        }
        .controls {
            margin-bottom: 15px;
        }
        .controls a {
            display: inline-block;
            padding: 8px 15px;
            margin-right: 10px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .controls a:hover {
            background-color: #0056b3;
        }
        .timestamp {
            color: #569cd6;
        }
        .qwen {
            color: #4ec9b0;
        }
        .deepseek {
            color: #dcdcaa;
        }
        .error {
            color: #f44747;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 API日志查看器</h1>
            <div class="controls">
                <a href="?lines=50">最近50条</a>
                <a href="?lines=100">最近100条</a>
                <a href="?lines=500">最近500条</a>
                <a href="?clear=1" onclick="return confirm('确定要清空日志吗？')">清空日志</a>
                <a href="javascript:location.reload()">刷新</a>
            </div>
        </div>
        
        <?php
        // 处理清空日志
        if (isset($_GET['clear']) && $_GET['clear'] == '1') {
            if (file_exists($logFile)) {
                file_put_contents($logFile, '');
                echo '<div style="color: green; margin-bottom: 15px;">✅ 日志已清空</div>';
            }
        }
        
        // 读取日志文件
        if (file_exists($logFile)) {
            $lines = file($logFile, FILE_IGNORE_NEW_LINES);
            $totalLines = count($lines);
            
            if ($totalLines > 0) {
                // 获取最后N行
                $displayLines = array_slice($lines, -$maxLines);
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "📊 总计 {$totalLines} 条日志，显示最近 " . count($displayLines) . " 条";
                echo "</div>";
                
                echo '<div class="log-content">';
                
                foreach ($displayLines as $line) {
                    $line = htmlspecialchars($line);
                    
                    // 高亮不同类型的日志
                    if (strpos($line, 'qwen:') !== false) {
                        $line = '<span class="qwen">' . $line . '</span>';
                    } elseif (strpos($line, 'deepseek:') !== false) {
                        $line = '<span class="deepseek">' . $line . '</span>';
                    } elseif (strpos($line, 'error:') !== false) {
                        $line = '<span class="error">' . $line . '</span>';
                    }
                    
                    // 高亮时间戳
                    $line = preg_replace('/(\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\])/', '<span class="timestamp">$1</span>', $line);
                    
                    echo $line . "\n";
                }
                
                echo '</div>';
            } else {
                echo '<div style="color: #666;">📝 日志文件为空</div>';
            }
        } else {
            echo '<div style="color: #666;">📝 日志文件不存在</div>';
        }
        ?>
    </div>
</body>
</html>
