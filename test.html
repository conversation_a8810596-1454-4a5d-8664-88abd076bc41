<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>驾照考试题目识别API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="url"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
            max-height: 500px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .curl-example {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .curl-example h3 {
            margin-top: 0;
            color: #495057;
        }
        .curl-command {
            background-color: #343a40;
            color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            word-break: break-all;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 驾照考试题目识别API测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="imageUrl">图片URL地址:</label>
                <input 
                    type="url" 
                    id="imageUrl" 
                    name="imageUrl" 
                    placeholder="https://example.com/exam-image.jpg"
                    required
                >
            </div>
            
            <button type="submit" id="submitBtn">🔍 开始识别题目</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div class="curl-example">
            <h3>📋 cURL命令示例:</h3>
            <p>您也可以使用以下cURL命令来测试API：</p>
            <div class="curl-command" id="curlCommand">
                curl -X POST -H "Content-Type: application/json" -d '{"image_url":"YOUR_IMAGE_URL"}' http://localhost/index.php
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const imageUrl = document.getElementById('imageUrl').value;
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            const curlCommand = document.getElementById('curlCommand');
            
            // 更新cURL命令示例
            curlCommand.textContent = `curl -X POST -H "Content-Type: application/json" -d '{"image_url":"${imageUrl}"}' ${window.location.origin}/index.php`;
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 正在处理...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在调用API，请稍候...\n\n这可能需要几十秒的时间，因为需要：\n1. 调用Qwen-VL-Plus识别图片\n2. 调用DeepSeek-Chat解答题目\n3. 记录日志';
            
            try {
                const response = await fetch('/index.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_url: imageUrl
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';

                    let resultText = '✅ API调用成功！\n\n';

                    // 显示Qwen统计信息
                    if (data.qwen_response) {
                        const qwen = data.qwen_response;
                        resultText += '=== 🖼️ Qwen-VL-Plus 统计 ===\n';
                        resultText += `耗时: ${qwen.duration_ms || '未知'}ms\n`;
                        if (qwen.tokens) {
                            resultText += `Token消费: 总计${qwen.tokens.total_tokens} (输入${qwen.tokens.input_tokens} + 输出${qwen.tokens.output_tokens})\n`;
                        }
                        resultText += '识别结果:\n' + qwen.content + '\n\n';
                    }

                    // 显示DeepSeek统计信息
                    if (data.deepseek_response) {
                        const deepseek = data.deepseek_response;
                        resultText += '=== 🧠 DeepSeek-Chat 统计 ===\n';
                        resultText += `耗时: ${deepseek.duration_ms || '未知'}ms\n`;
                        if (deepseek.tokens) {
                            resultText += `Token消费: 总计${deepseek.tokens.total_tokens} (输入${deepseek.tokens.input_tokens} + 输出${deepseek.tokens.output_tokens})\n`;
                        }
                        resultText += '解答结果:\n' + deepseek.content;
                    }

                    resultDiv.textContent = resultText;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ API调用失败\n\n错误信息: ' + (data.error || '未知错误');
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 网络错误\n\n' + error.message;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🔍 开始识别题目';
            }
        });
    </script>
</body>
</html>
